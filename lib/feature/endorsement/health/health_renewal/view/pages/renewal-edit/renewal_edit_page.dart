import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/common/view/acko_scaffold.dart';
import 'package:acko_flutter/common/view/dashed_line.dart';
import 'package:acko_flutter/common/view/layout_header.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/cubit/renewal-edit-cubit/renewal_edit_cubit.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/enum/policy_type.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/validation_models/family_constrains.dart';
import 'package:acko_flutter/feature/endorsement/shared/forms/ppe_member_details_form.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/custom_expansion_tile.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/pricing_footer.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/state_provider/StateProvider.dart';

class RenewalEditPage extends StatefulWidget {
  const RenewalEditPage({super.key, required this.proposalDetails});

  final HealthJourneyManagerResponse proposalDetails;

  @override
  State<RenewalEditPage> createState() => _RenewalEditPageState();
}

class _RenewalEditPageState extends State<RenewalEditPage>
    implements StateListener {
  StateProvider _stateProvider = StateProvider();
  RenewalEditCubit? _cubit;

  @override
  void initState() {
    super.initState();
    _stateProvider.subscribe(this);
    _cubit = BlocProvider.of<RenewalEditCubit>(context);
    context
        .read<RenewalEditCubit>()
        .getMemberData(widget.proposalDetails.toJson());
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_PPE_EDIT) {
      context
          .read<RenewalEditCubit>()
          .getMemberData(widget.proposalDetails.toJson(), force: true);
    }
  }

  @override
  dispose() {
    super.dispose();
    _stateProvider.dispose(this);
  }

  final HealthJourneyManagerUtils _healthJMUtil = HealthJourneyManagerUtils();
  FullPageLoader _fullPageLoader = FullPageLoader.instance;

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RenewalEditCubit, RenewalEditState>(
      listenWhen: (previous, current) => previous.status != current.status,
      listener: (context, state) {
        if (state.status.showFormEditingLoader) {
          _fullPageLoader.showFullPageLoader(context);
        } else if (state.status.dismissFormEditingLoader) {
          _fullPageLoader.dismissFullPageLoader(context);
        } else if (state.status.showErrorToast) {
          _healthJMUtil.showToastMessage(message: "Something went wrong");
        }
      },
      buildWhen: (prev, curr) =>
          curr.status.isLoading ||
          curr.status.isFailure ||
          curr.status.isSuccess,
      builder: (context, state) {
        if (state.status.isSuccess) {
          bool isAsp = _cubit?.policyType == PolicyType.AROGYASANJIVINI;
          int memberCount = (state.formValues?.newValues?.memberDetails
                  ?.where((member) => member?.isRemoved == false)
                  .length ??
              0);
          bool showAddMemberCta = isAsp ? memberCount < 5 : memberCount < 9;
          return CommonScaffold.setScaffold(
            SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 16,
                    ),
                    TextEuclidSemiBold18(
                      'Edit policy',
                      textSize: 24.0,
                    ),
                    SizedBox(
                      height: 8,
                    ),
                    TextEuclidRegular14(
                      'You might have to upload your documents for verification',
                      textColor: color5B5675,
                      textSize: 16.0,
                    ),
                    SizedBox(
                      height: 24,
                    ),
                    CustomExpansionTile(
                      title: "Member details",
                      content: Column(
                        children: [
                          if (state.formValues?.newValues?.proposerDetails !=
                              null) ...[
                            MemberDetailsForm(
                              validationConfig: state.vaidationConfig ??
                                  EndorsementFormValidatorConfig(
                                    familyConstrains: [],
                                  ),
                              oldMemberDetails:
                                  state.formValues?.oldValues?.proposerDetails,
                              newMemberDetails:
                                  state.formValues?.newValues?.proposerDetails,
                              memberIndex: 1,
                              updateMemberDetails: (String key,
                                      String insuredId,
                                      String newValue,
                                      bool isProposer,
                                      bool hasError,
                                      bool isFinancial) =>
                                  context
                                      .read<RenewalEditCubit>()
                                      .findAndUpdateMemberDetails(
                                          key: key,
                                          insuredId: insuredId,
                                          newValue: newValue,
                                          isProposer: isProposer,
                                          hasError: hasError,
                                          isFinancial: isFinancial),
                              addMemberBack: (String insuredId) => context
                                  .read<RenewalEditCubit>()
                                  .addMemberBack(insuredId),
                              removeMember: (String insuredId) => context
                                  .read<RenewalEditCubit>()
                                  .removeMember(insuredId),
                              enableAddBack: false,
                              isRenewalFlow: true,
                            ),
                          ],
                          // list of members

                          if (state.formValues?.newValues?.memberDetails
                                  .isNotNullOrEmpty ??
                              false)
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: DottedLine(
                                dashColor: colorE7E7F0,
                              ),
                            ),
                          ListView.separated(
                              padding: EdgeInsets.zero,
                              shrinkWrap: true,
                              physics: NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) =>
                                  MemberDetailsForm(
                                    validationConfig: state.vaidationConfig ??
                                        EndorsementFormValidatorConfig(
                                          familyConstrains: [],
                                        ),
                                    oldMemberDetails: state
                                                    .formValues
                                                    ?.oldValues
                                                    ?.memberDetails !=
                                                null &&
                                            index >= 0 &&
                                            index <
                                                state.formValues!.oldValues!
                                                    .memberDetails!.length
                                        ? state.formValues!.oldValues!
                                            .memberDetails![index]
                                        : null,
                                    newMemberDetails: state.formValues
                                        ?.newValues?.memberDetails?[index],
                                    memberIndex: state.formValues?.newValues
                                                ?.proposerDetails !=
                                            null
                                        ? index + 2
                                        : index + 1,
                                    updateMemberDetails: (String key,
                                            String insuredId,
                                            String newValue,
                                            bool isProposer,
                                            bool hasError,
                                            bool isFinancial) =>
                                        context
                                            .read<RenewalEditCubit>()
                                            .findAndUpdateMemberDetails(
                                                key: key,
                                                insuredId: insuredId,
                                                newValue: newValue,
                                                isProposer: isProposer,
                                                hasError: hasError,
                                                isFinancial: isFinancial),
                                    addMemberBack: (String insuredId) => context
                                        .read<RenewalEditCubit>()
                                        .addMemberBack(insuredId),
                                    removeMember: (String insuredId) => context
                                        .read<RenewalEditCubit>()
                                        .removeMember(insuredId),
                                    enableAddBack: false,
                                    isRenewalFlow: true,
                                  ),
                              separatorBuilder: (context, index) => Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: DottedLine(
                                      dashColor: colorE7E7F0,
                                    ),
                                  ),
                              itemCount: state.formValues?.newValues
                                      ?.memberDetails?.length ??
                                  0),
                          if (true) SizedBox(height: 16),
                          if (showAddMemberCta)
                            _buildAddNewMember(state.policyNumber!)
                        ],
                      ),
                      initiallyExpanded: true,
                    ),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: _getPricingFooter(
                amount: state.amount,
                enableCta: state.enableCta,
                totalEdits: state.totalEdits),
            appBarModel: AppBarModel(
              context: context,
              pageName: '',
              onBackPressed: () {
                Navigator.pop(context);
              },
              onHelpPressed: () {
                Navigator.pushNamed(context, Routes.WEB_PAGE, arguments: {
                  "url": "${Constants.BASE_URL}gi/p/health/help"
                });
              },
            ),
          );
        } else if (state.status.isLoading)
          return _getLoadingView();
        else
          return _getErrorView();
      },
    );
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView() {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: something_went_wrong,
        subTitle: api_something_went_wrong_sory,
        btnTitle: go_back,
        isOutlinedButton: true,
        onTap: () => Navigator.pop(context),
        imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
      ),
    );
  }

  _buildAddNewMember(String policyNumber) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: GestureDetector(
        onTap: () async {
          // todo: check why its not working
          await _cubit?.syncApiAndFormValues(
              nextNode: RenewalNodes.UPDATE_MEMBERS_DETAIL);

          Navigator.pushNamed(context, Routes.PPE_ADD_MEMBER_SCREEN,
              arguments: {
                "policy_number": policyNumber,
                "journey_type": JourneyType.RENEWAL,
                "data": _cubit?.state.postPolicyResponse,
                "is_asp_policy": _cubit?.policyType == null
                    ? false
                    : _cubit?.policyType == PolicyType.AROGYASANJIVINI
              });
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Icon(Icons.add_circle_outline_rounded,
                color: color1B73E8, size: 16),
            SDUIText(
              value: "Add member",
              textStyle: "lSmall",
              textColor: color1B73E8,
              padding: EdgeInsets.only(left: 4),
            ),
          ],
        ),
      ),
    );
  }

  _getPricingFooter({num? amount, bool? enableCta, int? totalEdits}) {
    bool isPricingLoading = false;
    if (isPricingLoading == false) {
      return PricingFooter(
          price: amount != null
              ? "₹ ${NumberUtils.commaSeparatedNumber(amount, showDecimal: true)}"
              : "",
          totalEdits: totalEdits,
          onTotalEditsTap: () {
            //TODO(ayush): uncomment this when the review changes sheet is required
            // context.showAckoModalBottomSheet(
            //     child: BlocProvider.value(
            //   value: context.read<RenewalEditCubit>(),
            //   child: ReviewChangesSheet(
            //     policyChangesData: state,
            //   ),
            // ));
          },
          onContinue: () async {
            await _cubit?.syncApiAndFormValues(
                nextNode: RenewalNodes.UPDATE_MEMBERS_DETAIL);
            final _state = _cubit?.state;
            final result = await Navigator.pushNamed(
              context,
              Routes.HEALTH_RENEWAL_REVIEW_PAGE,
              arguments: {
                'member_details': _state?.postPolicyResponse,
              },
            );
            if (result == null) return;
            _cubit?.documents = result as List<Documents>;
          },
          enableCta: enableCta);
    } else if (isPricingLoading == true) {
      return PricingFooterSkeleton();
    }
  }
}
